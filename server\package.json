{"name": "server", "version": "1.0.0", "description": "fruits shop website", "license": "ISC", "author": "sunil and pintu", "type": "commonjs", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "gridfs-stream": "^1.1.1", "instamojo-nodejs": "^0.0.5", "jsonwebtoken": "^9.0.2", "mongodb": "^6.14.2", "mongoose": "^8.12.0", "nodemon": "^3.1.9", "openurl": "^1.1.1", "scripts": "^0.1.0", "stripe": "^18.2.0"}}