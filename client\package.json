{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@stripe/stripe-js": "^7.3.1", "axios": "^1.7.9", "cra-template": "^1.3.0", "framer-motion": "^12.4.7", "lucide-react": "^0.477.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^7.2.0", "react-scripts": "^5.0.1", "react-toastify": "^11.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.3", "tailwindcss": "3", "vite": "^6.2.1"}}